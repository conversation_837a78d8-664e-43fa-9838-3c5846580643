import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:flutter_chat_ui/flutter_chat_ui.dart';
import 'package:toii_social/cubit/ai_chat/ai_chat_cubit.dart';
import 'package:toii_social/locator/locator.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';

class AiChatScreen extends StatelessWidget {
  const AiChatScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => AiChatCubit(aiChatRepository: serviceLocator()),
      child: const _AiChatView(),
    );
  }
}

class _AiChatView extends StatelessWidget {
  const _AiChatView();

  @override
  Widget build(BuildContext context) {
    return BaseScaffold(
      title: Text(
        'Chat with Gao',
        style: headlineMedium.copyWith(
          color: context.themeData.primaryGreen500,
        ),
      ),
      body: BlocBuilder<AiChatCubit, AiChatState>(
        builder: (context, state) {
          return Stack(
            children: [
              Chat(
                messages: state.messages,
                onSendPressed:
                    (message) => _handleSendPressed(context, message),
                user: context.read<AiChatCubit>().user,
                theme: _buildChatTheme(context),
                showUserAvatars: true,
                showUserNames: true,
                inputOptions: InputOptions(
                  sendButtonVisibilityMode: SendButtonVisibilityMode.always,
                  // Disable input while streaming
                  enabled: state.status != AiChatStatus.streaming,
                ),
                l10n: const ChatL10nEn(inputPlaceholder: 'Ask Gao anything...'),
              ),
              // Show loading indicator when streaming
              if (state.status == AiChatStatus.streaming)
                Positioned(
                  top: 16,
                  left: 0,
                  right: 0,
                  child: Center(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: context.themeData.primaryGreen500.withValues(
                          alpha: 0.9,
                        ),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Gao is typing...',
                            style: bodySmall.copyWith(color: Colors.white),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }

  void _handleSendPressed(BuildContext context, types.PartialText message) {
    context.read<AiChatCubit>().sendMessage(message.text);
  }

  ChatTheme _buildChatTheme(BuildContext context) {
    final themeData = context.themeData;

    return DefaultChatTheme(
      backgroundColor: themeData.scaffoldBackgroundColor,
      primaryColor: themeData.primaryGreen500,
      secondaryColor: themeData.primaryGreen100,
      inputBackgroundColor: themeData.cardColor,
      inputTextColor: themeData.textTheme.bodyLarge?.color ?? Colors.black,
      messageBorderRadius: 12,
      userAvatarNameColors: [themeData.primaryGreen500],
      inputBorderRadius: const BorderRadius.all(Radius.circular(24)),
      inputPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      inputMargin: const EdgeInsets.all(16),
      sentMessageBodyTextStyle: bodyMedium.copyWith(color: Colors.white),
      receivedMessageBodyTextStyle: bodyMedium.copyWith(
        color: themeData.textTheme.bodyLarge?.color,
      ),
      sentMessageCaptionTextStyle: bodySmall.copyWith(color: Colors.white70),
      receivedMessageCaptionTextStyle: bodySmall.copyWith(
        color: themeData.textTheme.bodySmall?.color,
      ),
    );
  }
}
