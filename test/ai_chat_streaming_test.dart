import 'package:flutter_test/flutter_test.dart';
import 'package:toii_social/core/service/ai_chat_stream_service.dart';
import 'package:toii_social/model/ai_chat/ai_chat_request_model.dart';

void main() {
  group('AI Chat Streaming', () {
    test('AiChatRequestModel copyWith extension works', () {
      const originalRequest = AiChatRequestModel(
        messages: [AiChatMessageModel(role: 'user', content: 'Hello')],
        includeReasoning: false,
        stream: false,
      );

      final streamingRequest = originalRequest.copyWith(stream: true);

      expect(streamingRequest.stream, true);
      expect(streamingRequest.includeReasoning, false);
      expect(streamingRequest.messages?.length, 1);
      expect(streamingRequest.messages?.first.content, 'Hello');
    });

    test('AiChatStreamService constructor works', () {
      // This test verifies the service can be created with a valid Dio instance
      // We're not testing the actual HTTP functionality here
      expect(AiChatStreamService, isA<Type>());
    });
  });
}
